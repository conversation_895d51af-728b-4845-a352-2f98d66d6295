// API utility functions for making authenticated requests

/**
 * Get the stored authentication token from localStorage or sessionStorage
 */
export const getAuthToken = (): string | null => {
  // Try localStorage first, then sessionStorage
  const token = localStorage.getItem("access_token") || sessionStorage.getItem("access_token")
  return token
}

/**
 * Get the token type (usually 'bearer')
 */
export const getTokenType = (): string => {
  const tokenType = localStorage.getItem("token_type") || sessionStorage.getItem("token_type")
  return tokenType || "bearer"
}

/**
 * Get the API base URL from environment variables
 */
export const getApiBaseUrl = (): string => {
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
  if (!apiBaseUrl) {
    throw new Error("API base URL not configured")
  }
  return apiBaseUrl
}

/**
 * Create authorization headers for API requests
 */
export const getAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken()
  const tokenType = getTokenType()
  
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept": "application/json",
  }
  
  if (token) {
    headers["Authorization"] = `${tokenType.charAt(0).toUpperCase() + tokenType.slice(1)} ${token}`
  }
  
  return headers
}

/**
 * Make an authenticated API request
 */
export const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  const apiBaseUrl = getApiBaseUrl()
  const headers = getAuthHeaders()
  
  const response = await fetch(`${apiBaseUrl}${endpoint}`, {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  })
  
  return response
}

// TypeScript interfaces for API responses
export interface OutlookAuthResponse {
  success: boolean
  auth_url: string
  state: string
  message: string
}

export interface OutlookAuthError {
  success: false
  message: string
  detail?: string
}

// TypeScript interfaces for Outlook connections (matching real API response)
export interface OutlookConnection {
  connection_id: string
  user_email: string
  status: "Connected" | "Disconnected" | "Error" | "Syncing"
  is_active: boolean
  is_token_valid: boolean
  created_at: string
  last_sync_at: string | null
  token_expires_at: string
  email_count: number
  sync_interval_seconds: number
  scopes: string[]
  error_message: string | null
  health_status: string
}

export interface OutlookConnectionsResponse {
  success: boolean
  message: string
  admin_email: string
  connections: OutlookConnection[]
  total_connections: number
  active_connections: number
  sync_interval_seconds: number
}

export interface OutlookConnectionsError {
  success: false
  message: string
  detail?: string
}

export interface OutlookMonitorStatus {
  is_running: boolean
  sync_interval: number
  error_retry_delay: number
  task_status: string
  monitoring_mode: string
  monitored_connections: any | null
  monitored_connection_count: number | null
  active_connections_found: number
}

export interface OutlookMonitorConfigResponse {
  success: boolean
  message: string
  status: OutlookMonitorStatus
}

// TypeScript interfaces for QuickBooks integration
export interface QuickBooksConnectionData {
  realm_id: string
  client_id: string
  client_secret: string
  access_token: string
  refresh_token: string
  token_expires_at: string
  environment: "sandbox" | "production"
}

export interface QuickBooksConnectionResponse {
  success: boolean
  message: string
  connection_status: {
    is_connected: boolean
    realm_id: string
    environment: string
    token_expires_at: string
    token_status: string
    last_tested: string | null
    company_name: string | null
    company_id: string | null
  }
  timestamp: string
}

export interface QuickBooksConnection {
  id: string
  realm_id: string
  client_id: string
  environment: string
  token_expires_at: string
  created_at: string
  updated_at: string
  last_tested: string | null
  is_active: boolean
  company_info: any | null
}

export interface QuickBooksConnectionsListResponse {
  success: boolean
  message: string
  connections: QuickBooksConnection[]
  pagination: {
    total: number
    skip: number
    limit: number
    has_more: boolean
  }
}

export interface QuickBooksConnectionError {
  success: false
  message: string
  detail?: string
}

// TypeScript interfaces for Invoices API
export interface Invoice {
  id: string
  invoice_number: string
  vendor: string
  invoice_date: string
  due_date: string | null
  category: string
  amount: number
  status: string
  source: string
  total_amount: number
  currency: string
  created_at: string
  updated_at: string
}

export interface InvoicesResponse {
  success: boolean
  message: string
  invoices: Invoice[]
  pagination: {
    skip: number
    limit: number
    total: number
    has_next: boolean
    has_previous: boolean
  }
  filters: {
    status?: string
    vendor?: string
    category?: string
    date_from?: string
    date_to?: string
  }
}

export interface InvoicesError {
  success: false
  message: string
  detail?: string
}

// TypeScript interfaces for Invoice Details API
export interface InvoiceLineItem {
  description: string
  quantity: number
  unit_price: number
  total_price: number // Note: API uses total_price, not total
}

export interface VendorMatch {
  vendor_name: string
  existing_vendor_id: string | null
  needs_creation: boolean
  confidence_score: number
}

export interface AccountingHeadMatch {
  extracted_accounting_head: string
  suggested_accounting_head: string
  existing_head_id: string
  needs_creation: boolean
  confidence_score: number
}

export interface JobInfo {
  job_id: string
  original_filename: string
  file_size: number
  content_type: string
  uploaded_file_path: string
  created_at: string
  processed_at: string
}

export interface InvoiceDetailsFormatted {
  invoice_number: string
  vendor: string
  vendor_address: string
  customer: string
  customer_address: string
  invoice_date: string
  due_date: string | null
  category: string
  amount: number
  currency: string
  tax_amount: number | null
  line_items: InvoiceLineItem[]
  source: string
  status: string
  added_date: string
  processed_date: string | null
  uploaded_by: string
}

export interface InvoiceDetailsResponse {
  success: boolean
  invoice: {
    _id: string
    job_id: string
    uploaded_file_path: string
    uploaded_by: string
    is_invoice: boolean
    is_continuation: boolean
    invoice_id: string
    invoice_date: string
    due_date: string | null
    accounting_head: string
    vendor_name: string
    vendor_address: string
    customer_name: string
    customer_address: string
    total_amount: number
    tax_amount: number | null
    currency: string
    line_items: InvoiceLineItem[]
    start_page: number
    vendor_match: VendorMatch
    accounting_head_match: AccountingHeadMatch
    processing_status: string
    created_at: string
    processed_at: string
    extracted_text?: string
  }
  job_info: JobInfo
  formatted: InvoiceDetailsFormatted
}

export interface InvoiceDetailsError {
  success: false
  message: string
  detail?: string
}

// TypeScript interfaces for Invoice Upload API
export interface InvoiceUploadResponse {
  success: boolean
  message: string
  job_id: string
  file_path: string
  error: string | null
}

export interface InvoiceUploadError {
  success: false
  message: string
  detail?: string
}

/**
 * Get Outlook authorization URL
 */
export const getOutlookAuthUrl = async (openBrowser: boolean = true): Promise<OutlookAuthResponse> => {
  try {
    const response = await makeAuthenticatedRequest(
      `/api/v1/outlook/auth/url?open_browser=${openBrowser}`,
      {
        method: "POST",
        body: "",
      }
    )

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to get Outlook authorization URL")
    }

    return data as OutlookAuthResponse
  } catch (error) {
    console.error("Error getting Outlook auth URL:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while connecting to Outlook.")
    }
  }
}

/**
 * Check if user is authenticated (has valid token)
 */
export const isAuthenticated = (): boolean => {
  const token = getAuthToken()
  const timestamp = localStorage.getItem("login_timestamp") || sessionStorage.getItem("login_timestamp")
  const expiresIn = localStorage.getItem("expires_in") || sessionStorage.getItem("expires_in")

  if (!token || !timestamp || !expiresIn) {
    return false
  }

  const loginTime = parseInt(timestamp)
  const expirationTime = parseInt(expiresIn) * 1000 // Convert to milliseconds
  const currentTime = Date.now()

  return (currentTime - loginTime) < expirationTime
}

/**
 * Fetch invoices list with pagination and filters
 */
export const getInvoices = async (
  skip: number = 0,
  limit: number = 10,
  filters?: {
    status?: string
    vendor?: string
    category?: string
    date_from?: string
    date_to?: string
  }
): Promise<InvoicesResponse> => {
  try {
    // Build query parameters
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    })

    // Add filters if provided
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') {
          params.append(key, value)
        }
      })
    }

    const response = await makeAuthenticatedRequest(`/api/v1/invoices/list?${params.toString()}`)

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to fetch invoices")
    }

    return data as InvoicesResponse
  } catch (error) {
    console.error("Error fetching invoices:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while fetching invoices.")
    }
  }
}

/**
 * Fetch detailed Outlook connections
 */
export const getOutlookConnections = async (): Promise<OutlookConnectionsResponse> => {
  try {
    const response = await makeAuthenticatedRequest("/api/v1/outlook/connections/detailed")

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to fetch Outlook connections")
    }

    return data as OutlookConnectionsResponse
  } catch (error) {
    console.error("Error fetching Outlook connections:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while fetching Outlook connections.")
    }
  }
}

/**
 * Configure Outlook email monitoring
 */
export const configureOutlookMonitoring = async (syncInterval: number): Promise<OutlookMonitorConfigResponse> => {
  try {
    const response = await makeAuthenticatedRequest(`/api/v1/outlook/monitor/configure?sync_interval=${syncInterval}`, {
      method: "POST",
      body: "",
    })

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to configure email monitoring")
    }

    return data as OutlookMonitorConfigResponse
  } catch (error) {
    console.error("Error configuring Outlook monitoring:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while configuring email monitoring.")
    }
  }
}

/**
 * Connect QuickBooks integration
 */
export const connectQuickBooks = async (connectionData: QuickBooksConnectionData): Promise<QuickBooksConnectionResponse> => {
  try {
    const response = await makeAuthenticatedRequest("/api/v1/quickbooks/connections", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(connectionData),
    })

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to connect QuickBooks")
    }

    return data as QuickBooksConnectionResponse
  } catch (error) {
    console.error("Error connecting QuickBooks:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while connecting QuickBooks.")
    }
  }
}

/**
 * Get QuickBooks connections list
 */
export const getQuickBooksConnections = async (
  skip: number = 0,
  limit: number = 50,
  activeOnly: boolean = true
): Promise<QuickBooksConnectionsListResponse> => {
  try {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
      active_only: activeOnly.toString()
    })

    const response = await makeAuthenticatedRequest(`/api/v1/quickbooks/connections?${params.toString()}`)

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to fetch QuickBooks connections")
    }

    return data as QuickBooksConnectionsListResponse
  } catch (error) {
    console.error("Error fetching QuickBooks connections:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while fetching QuickBooks connections.")
    }
  }
}

/**
 * Fetch invoice details by ID
 */
export const getInvoiceDetails = async (invoiceId: string): Promise<InvoiceDetailsResponse> => {
  try {
    const response = await makeAuthenticatedRequest(`/api/v1/invoices/details/${invoiceId}`)

    const data = await response.json()

    if (!response.ok || !data.success) {
      if (response.status === 404) {
        throw new Error("Invoice not found")
      } else if (response.status === 403) {
        throw new Error("You don't have permission to view this invoice")
      } else {
        throw new Error(data.message || "Failed to fetch invoice details")
      }
    }

    return data as InvoiceDetailsResponse
  } catch (error) {
    console.error("Error fetching invoice details:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while fetching invoice details.")
    }
  }
}

/**
 * Upload invoice file
 */
export const uploadInvoice = async (file: File): Promise<InvoiceUploadResponse> => {
  try {
    const apiBaseUrl = getApiBaseUrl()
    const token = getAuthToken()

    if (!token) {
      throw new Error("Authentication token not found. Please log in again.")
    }

    // Create FormData for file upload
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${apiBaseUrl}/api/v1/invoices/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
      },
      body: formData,
    })

    const data = await response.json()

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Failed to upload invoice")
    }

    return data as InvoiceUploadResponse
  } catch (error) {
    console.error("Error uploading invoice:", error)

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error("Unable to connect to the server. Please check your internet connection.")
    } else if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred while uploading the invoice.")
    }
  }
}
